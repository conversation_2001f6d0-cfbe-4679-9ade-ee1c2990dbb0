"""
Authentication Blueprint

Handles authentication and session management routes including:
- Admin login/logout
- Session management
- CSRF token handling
- User authentication flows

This blueprint manages all authentication-related functionality
that was previously in the main app.py file.
"""

import logging
from flask import Blueprint, request, jsonify, flash, redirect, url_for, session
import user_management as um
from flask_wtf import csrf

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/admin/login', methods=['POST'])
def admin_login():
    """
    Handle admin login directly from the dashboard.
    
    Processes login form data and authenticates users.
    Handles password expiration and device fingerprinting.
    
    Returns:
        Redirect to dashboard on success or back to login on failure
    """
    try:
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            remember = request.form.get('remember') == 'on'

            # Validate input
            if not username or not password:
                flash('Username and password are required.', 'error')
                return redirect(url_for('admin.admin_dashboard'))

            # Authenticate user
            user, error = um.authenticate_user(username, password)

            if user:
                # Check if password is expired
                if user.password_expired:
                    # Store user ID in session for password change
                    session['password_expired_user_id'] = user.user_id
                    flash('Your password has expired. Please create a new password.', 'warning')
                    return redirect(url_for('user.change_expired_password'))

                # Log in user
                um.login_user(user, remember=remember)

                # Store device fingerprint if available
                device_fingerprint = request.form.get('device_fingerprint')
                if device_fingerprint:
                    session['device_fingerprint'] = device_fingerprint

                flash(f'Welcome, {user.username}!', 'success')
                return redirect(url_for('admin.admin_dashboard'))
            else:
                flash(error, 'error')
                return redirect(url_for('admin.admin_dashboard'))
                
    except Exception as e:
        logger.error(f"Error during admin login: {str(e)}")
        flash('An error occurred during login. Please try again.', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@auth_bp.route('/clear_session', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection since this is called during page unload
def clear_session():
    """
    Clear all session data when the app is closed.
    
    This endpoint is called when users close the application
    to clean up session data and ensure proper logout.
    
    Returns:
        JSON response indicating success
    """
    try:
        # Get session info before clearing
        session_id = session.get('session_id')
        client_name = session.get('client_name', 'Anonymous')
        
        # Log session clearing
        if session_id:
            logger.info(f"Clearing session {session_id} for client {client_name}")
        
        # Clear all session data
        session.clear()
        
        return jsonify({
            "success": True,
            "message": "Session cleared successfully"
        }), 200
        
    except Exception as e:
        logger.error(f"Error clearing session: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Failed to clear session"
        }), 500

@auth_bp.route('/api/csrf-token', methods=['GET'])
def get_csrf_token():
    """
    Get a fresh CSRF token.
    
    This endpoint provides CSRF tokens for AJAX requests
    and handles token refresh when tokens expire.
    
    Returns:
        JSON response with CSRF token
    """
    try:
        # Generate a fresh CSRF token
        token = csrf.generate_csrf()
        
        return jsonify({
            "success": True,
            "csrf_token": token
        }), 200
        
    except Exception as e:
        logger.error(f"Error generating CSRF token: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Failed to generate CSRF token"
        }), 500

@auth_bp.route('/admin/session/<session_id>/close', methods=['POST'])
def close_session(session_id):
    """
    Close a session by updating its end time.
    
    This endpoint allows administrators to manually close
    user sessions and update session records.
    
    Args:
        session_id: The session ID to close
        
    Returns:
        JSON response indicating success or failure
    """
    try:
        # Import here to avoid circular imports
        import db_utils
        
        # Close the session in the database
        success = db_utils.close_session(session_id)
        
        if success:
            logger.info(f"Session {session_id} closed successfully")
            return jsonify({
                "success": True,
                "message": "Session closed successfully"
            }), 200
        else:
            logger.warning(f"Failed to close session {session_id}")
            return jsonify({
                "success": False,
                "error": "Failed to close session"
            }), 500
            
    except Exception as e:
        logger.error(f"Error closing session {session_id}: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Error closing session: {str(e)}"
        }), 500

# Authentication decorators (moved from app.py)
from functools import wraps

def admin_required(f):
    """
    Decorator to require admin authentication for routes.
    
    This decorator checks if a user is authenticated and has
    appropriate permissions to access admin functionality.
    
    Args:
        f: Function to wrap
        
    Returns:
        Wrapped function with authentication check
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Skip authentication for the admin dashboard (which handles login)
        if request.endpoint == 'admin.admin_dashboard':
            return f(*args, **kwargs)

        # For all other admin routes, require authentication
        if not um.current_user.is_authenticated:
            # Check if this is an API request
            if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                return jsonify({"error": "Authentication required", "redirect": url_for('admin.admin_dashboard')}), 401
            else:
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('admin.admin_dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def function_permission_required(function_name):
    """
    Decorator to require specific dashboard function permission.
    
    This decorator checks if the authenticated user has permission
    to access specific dashboard functions.
    
    Args:
        function_name: Name of the function to check permission for
        
    Returns:
        Decorator function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip permission check for admin dashboard
            if request.endpoint == 'admin.admin_dashboard':
                return f(*args, **kwargs)

            # Require authentication
            if not um.current_user.is_authenticated:
                # Check if this is an API request
                if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                    return jsonify({"error": "Authentication required", "redirect": url_for('admin.admin_dashboard')}), 401
                else:
                    flash('Please log in to access this page.', 'error')
                    return redirect(url_for('admin.admin_dashboard'))

            # Check if user has permission for this function
            if not um.current_user.has_dashboard_permission(function_name) and um.current_user.role != 'admin':
                # Check if this is an API request
                if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                    return jsonify({"error": f"Permission denied for {function_name.replace('_', ' ').title()}", "redirect": url_for('admin.admin_dashboard')}), 403
                else:
                    flash(f'You do not have permission to access {function_name.replace("_", " ").title()}.', 'error')
                    return redirect(url_for('admin.admin_dashboard'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator
