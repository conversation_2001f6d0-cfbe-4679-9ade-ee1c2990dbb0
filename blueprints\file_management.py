"""
File Management Blueprint

Handles file upload, processing, and management routes including:
- File upload interface
- Category management
- File listing and deletion
- Vector data viewing
- Duplicate checking

This blueprint manages all file-related operations that were
previously in the main app.py file.
"""

import os
import logging
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from werkzeug.utils import secure_filename
import utils
import db_utils
from blueprints.auth import admin_required, function_permission_required
from utils import delete_file, check_duplicate_pdf
from db_embed import embed_file_db_first, scrape_url_db_first
from get_vector_db import get_vector_db

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
file_management_bp = Blueprint('file_management', __name__)

# Configuration
TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")
CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")

@file_management_bp.route('/admin/upload', methods=['GET', 'POST'])
@admin_required
@function_permission_required('upload_content')
def upload_file():
    """
    File upload interface and processing.
    
    Handles both GET requests to display the upload form
    and POST requests to process file uploads and URL scraping.
    
    Returns:
        Rendered upload template or redirect after processing
    """
    if request.method == 'GET':
        try:
            categories = sorted(utils.list_categories())
            return render_template('upload.html', categories=categories)
        except Exception as e:
            logger.error(f"Error loading upload page: {str(e)}")
            flash('Error loading upload page. Please try again.', 'error')
            return render_template('upload.html', categories=[])
    
    elif request.method == 'POST':
        try:
            # Get form data
            category = request.form.get('category', '').strip()
            pdf_url = request.form.get('pdf_url', '').strip()
            
            # Validate category
            if not category:
                flash('Category is required.', 'error')
                return redirect(url_for('file_management.upload_file'))
            
            # Handle file upload
            if 'file' in request.files:
                file = request.files['file']
                if file and file.filename:
                    return _handle_file_upload(file, category, pdf_url)
            
            # Handle URL scraping
            if pdf_url:
                return _handle_url_scraping(pdf_url, category)
            
            flash('Please select a file or enter a URL.', 'error')
            return redirect(url_for('file_management.upload_file'))
            
        except Exception as e:
            logger.error(f"Error during upload: {str(e)}")
            flash('Error during upload. Please try again.', 'error')
            return redirect(url_for('file_management.upload_file'))

def _handle_file_upload(file, category, pdf_url):
    """
    Handle file upload processing.
    
    Args:
        file: Uploaded file object
        category: Document category
        pdf_url: Optional source URL
        
    Returns:
        Redirect response with status message
    """
    try:
        # Get vision settings
        use_vision = request.form.get('use_vision', 'false').lower() == 'true'
        filter_sensitivity = request.form.get('filter_sensitivity', 'medium')
        max_images = int(request.form.get('max_images', 30))
        force_update = request.form.get('force_update', 'false').lower() == 'true'
        
        # Log vision settings
        if use_vision:
            logger.info(f"Embedding PDF with vision analysis enabled. "
                       f"Sensitivity: {filter_sensitivity}, Max images: {max_images}")
        else:
            logger.info("Embedding PDF with vision analysis disabled")
        
        # Process the file
        success, message = embed_file_db_first(
            file,
            category,
            source_url=pdf_url,
            use_vision=use_vision,
            filter_sensitivity=filter_sensitivity,
            max_images=max_images,
            force_update=force_update
        )
        
        if success:
            flash(message, 'success')
        else:
            flash(message, 'error')
            
        return redirect(url_for('file_management.upload_file'))
        
    except Exception as e:
        logger.error(f"Error handling file upload: {str(e)}")
        flash(f'Error processing file: {str(e)}', 'error')
        return redirect(url_for('file_management.upload_file'))

def _handle_url_scraping(pdf_url, category):
    """
    Handle URL scraping processing.
    
    Args:
        pdf_url: URL to scrape
        category: Document category
        
    Returns:
        Redirect response with status message
    """
    try:
        # Get vision settings
        use_vision = request.form.get('use_vision', 'false').lower() == 'true'
        filter_sensitivity = request.form.get('filter_sensitivity', 'medium')
        max_images = int(request.form.get('max_images', 30))
        force_update = request.form.get('force_update', 'false').lower() == 'true'
        
        # Process the URL
        success, message = scrape_url_db_first(
            pdf_url,
            category,
            use_vision=use_vision,
            filter_sensitivity=filter_sensitivity,
            max_images=max_images,
            force_update=force_update
        )
        
        if success:
            flash(message, 'success')
        else:
            flash(message, 'error')
            
        return redirect(url_for('file_management.upload_file'))
        
    except Exception as e:
        logger.error(f"Error handling URL scraping: {str(e)}")
        flash(f'Error processing URL: {str(e)}', 'error')
        return redirect(url_for('file_management.upload_file'))

@file_management_bp.route('/api/check_duplicate', methods=['POST'])
def check_duplicate():
    """
    API endpoint to check if a file is a duplicate before uploading.
    
    Checks for duplicate files based on filename and content hash
    to prevent unnecessary processing.
    
    Returns:
        JSON response with duplicate check results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        filename = data.get('filename')
        category = data.get('category')
        
        if not filename or not category:
            return jsonify({'error': 'Filename and category are required'}), 400
        
        # Check for duplicate
        is_duplicate, existing_file = check_duplicate_pdf(filename, category)
        
        return jsonify({
            'is_duplicate': is_duplicate,
            'existing_file': existing_file,
            'message': 'Duplicate check completed'
        }), 200
        
    except Exception as e:
        logger.error(f"Error checking duplicate: {str(e)}")
        return jsonify({'error': f'Error checking duplicate: {str(e)}'}), 500

@file_management_bp.route('/admin/files')
@admin_required
@function_permission_required('manage_files')
def list_files():
    """
    Display list of uploaded files organized by category.
    
    Shows files with their metadata, thumbnails, and management options.
    
    Returns:
        Rendered files listing template
    """
    try:
        files_data = {}
        categories = sorted(utils.list_categories())
        logger.info(f"Categories: {categories}")

        # Query SQLite database for URLs
        scraped_pages = db_utils.get_scraped_pages()
        url_map = {(page['category'], page['filename']): page['url'] for page in scraped_pages}

        for category in categories:
            category_path = os.path.join(TEMP_FOLDER, category)
            if os.path.isdir(category_path):
                files = []

                # Check for files in both old and new directory structures
                for filename in os.listdir(category_path):
                    file_path = os.path.join(category_path, filename)
                    
                    if os.path.isfile(file_path) and filename.lower().endswith('.pdf'):
                        # Get file info
                        file_info = _get_file_info(category, filename, url_map)
                        files.append(file_info)
                    elif os.path.isdir(file_path):
                        # Check subdirectories for PDFs
                        pdf_file = f"{filename}.pdf"
                        pdf_path = os.path.join(category_path, pdf_file)
                        if os.path.exists(pdf_path):
                            file_info = _get_file_info(category, pdf_file, url_map)
                            files.append(file_info)

                files_data[category] = sorted(files, key=lambda x: x['filename'])

        return render_template('files.html', files_data=files_data)
        
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        flash('Error loading files. Please try again.', 'error')
        return render_template('files.html', files_data={})

def _get_file_info(category, filename, url_map):
    """
    Get file information including metadata and thumbnails.
    
    Args:
        category: File category
        filename: File name
        url_map: Mapping of files to source URLs
        
    Returns:
        Dictionary with file information
    """
    try:
        file_path = os.path.join(TEMP_FOLDER, category, filename)
        file_stats = os.stat(file_path)
        
        # Get source URL if available
        source_url = url_map.get((category, filename))
        
        # Check for thumbnail
        pdf_name = os.path.splitext(filename)[0]
        thumbnail_path = os.path.join(TEMP_FOLDER, category, pdf_name, 'pdf_images', 'cover_image')
        thumbnail_url = None
        
        if os.path.exists(thumbnail_path):
            # Find the first image in the cover_image directory
            for img_file in os.listdir(thumbnail_path):
                if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    thumbnail_url = f"/{category}/{pdf_name}/pdf_images/cover_image/{img_file}"
                    break
        
        return {
            'filename': filename,
            'size': file_stats.st_size,
            'modified': file_stats.st_mtime,
            'source_url': source_url,
            'thumbnail_url': thumbnail_url
        }
        
    except Exception as e:
        logger.error(f"Error getting file info for {category}/{filename}: {str(e)}")
        return {
            'filename': filename,
            'size': 0,
            'modified': 0,
            'source_url': None,
            'thumbnail_url': None
        }

@file_management_bp.route('/admin/delete/<category>/<filename>', methods=['POST', 'DELETE'])
@admin_required
def delete_file_route(category, filename):
    """
    Delete a file and all its associated resources.

    Removes the file, images, tables, vector embeddings, and database entries.

    Args:
        category: File category
        filename: File name to delete

    Returns:
        JSON response with deletion status
    """
    try:
        # Call the enhanced delete_file function
        success, message = delete_file(category, filename)

        if success:
            logger.info(f"Successfully deleted file {category}/{filename}")
            return jsonify({
                'success': True,
                'message': message
            }), 200
        else:
            logger.warning(f"Failed to delete file {category}/{filename}: {message}")
            return jsonify({
                'success': False,
                'error': message
            }), 500

    except Exception as e:
        logger.error(f"Error deleting file {category}/{filename}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error deleting file: {str(e)}'
        }), 500

@file_management_bp.route('/admin/vector_data/<category>/<filename>')
def view_vector_data(category, filename):
    """
    View vector data for a specific file.

    Displays the vector embeddings and metadata for a document.

    Args:
        category: Document category
        filename: Document filename

    Returns:
        Rendered vector data template
    """
    try:
        db = get_vector_db(category)

        # Get vector data for the specific file
        results = db.get(where={"source": filename})

        return render_template('vector_data.html',
                             vector_data=results,
                             category=category,
                             filename=filename)

    except Exception as e:
        logger.error(f"Error viewing vector data for {category}/{filename}: {str(e)}")
        flash('Error loading vector data. Please try again.', 'error')
        return render_template('vector_data.html',
                             vector_data=None,
                             category=category,
                             filename=filename)

@file_management_bp.route('/admin/categories', methods=['GET', 'POST', 'DELETE'])
def list_categories_route():
    """
    Handle category management operations.

    Supports GET (list), POST (create), and DELETE (remove) operations
    for document categories.

    Returns:
        JSON response for API calls or rendered template for GET
    """
    if request.method == 'POST':
        try:
            # Handle category creation
            new_category = request.form.get('new_category', '').strip()

            if not new_category:
                return jsonify({'error': 'Category name is required'}), 400

            # Create category directory
            category_path = os.path.join(CHROMA_PATH, new_category)

            if os.path.exists(category_path):
                return jsonify({'error': 'Category already exists'}), 400

            os.makedirs(category_path, exist_ok=True)

            # Also create temp folder structure
            temp_category_path = os.path.join(TEMP_FOLDER, new_category)
            os.makedirs(temp_category_path, exist_ok=True)

            logger.info(f"Created new category: {new_category}")
            return jsonify({
                'success': True,
                'message': f'Category "{new_category}" created successfully'
            }), 200

        except Exception as e:
            logger.error(f"Error creating category: {str(e)}")
            return jsonify({'error': f'Error creating category: {str(e)}'}), 500

    elif request.method == 'DELETE':
        try:
            # Handle category deletion
            category = request.args.get('category', '').strip()

            if not category:
                return jsonify({'error': 'Category name is required'}), 400

            # Delete category and all associated resources
            success, message = _delete_category_completely(category)

            if success:
                return jsonify({
                    'success': True,
                    'message': message
                }), 200
            else:
                return jsonify({'error': message}), 500

        except Exception as e:
            logger.error(f"Error deleting category: {str(e)}")
            return jsonify({'error': f'Error deleting category: {str(e)}'}), 500

    else:  # GET request
        try:
            categories = sorted(utils.list_categories())

            # Return JSON for API requests
            if request.headers.get('Accept') == 'application/json':
                return jsonify({'categories': categories}), 200

            # Return template for web requests
            return render_template('categories.html', categories=categories)

        except Exception as e:
            logger.error(f"Error listing categories: {str(e)}")
            return jsonify({'error': 'Error listing categories'}), 500

def _delete_category_completely(category):
    """
    Delete a category and all its associated resources.

    Args:
        category: Category name to delete

    Returns:
        Tuple of (success, message)
    """
    try:
        import shutil

        # Delete vector database
        chroma_path = os.path.join(CHROMA_PATH, category)
        if os.path.exists(chroma_path):
            shutil.rmtree(chroma_path)

        # Delete temp files
        temp_path = os.path.join(TEMP_FOLDER, category)
        if os.path.exists(temp_path):
            shutil.rmtree(temp_path)

        # Delete database entries
        db_utils.delete_category_data(category)

        logger.info(f"Successfully deleted category: {category}")
        return True, f'Category "{category}" deleted successfully'

    except Exception as e:
        logger.error(f"Error deleting category {category}: {str(e)}")
        return False, f'Error deleting category: {str(e)}'
